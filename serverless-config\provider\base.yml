provider:
  name: aws
  stage: ${opt:stage, 'local'}
  region: ${env.REGION}
  versionFunctions: false
  deploymentBucket:
    name: ${env.DEPLOYMENT_BUCKET_NAME}
  stackTags:
    STAGE: ${self:provider.stage, 'local'}
    Subproject: "App"
    Project: "Serverless"
  tracing:
    apiGateway: ${env.XRAY_ENABLED}
    lambda: ${env.XRAY_ENABLED}
  vpc: ${file(./serverless-config/provider/vpc.yml):vpc}
  environment: ${file(./serverless-config/provider/environment.yml):environment}