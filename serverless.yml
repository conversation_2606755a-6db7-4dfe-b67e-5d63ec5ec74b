service: hennessy-tag-and-title

provider: ${file(./serverless-config/provider/base.yml):provider}

custom: ${file(./serverless-config/custom.yml):custom}

functions:
  - ${file(./serverless-config/resources/functions.yml):functions}

resources:
  - ${file(./serverless-config/resources/roles/bre_handler_role.yml)}
  - ${file(./serverless-config/resources/roles/bre_validation_role.yml)}

plugins: ${file(./serverless-config/plugins.yml):plugins}

package: ${file(./serverless-config/package.yml):package}