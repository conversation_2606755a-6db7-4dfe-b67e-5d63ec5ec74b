"""
Abstract base validator class
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Tuple


class BaseValidator(ABC):
    """Abstract base class for all validators"""
    
    @abstractmethod
    def validate(self, value: Any, rule: Dict, **kwargs) -> Tuple[bool, str]:
        """
        Abstract method for validation
        
        Args:
            value: The value to validate
            rule: The validation rule configuration
            **kwargs: Additional context parameters
            
        Returns:
            Tuple of (is_valid: bool, error_message: str)
        """
        pass
    
    def _get_error_message(self, rule: Dict, default_message: str) -> str:
        """
        Get error message from rule or return default
        
        Args:
            rule: The validation rule configuration
            default_message: Default error message if none specified
            
        Returns:
            Error message string
        """
        return rule.get("error_msg", default_message)
    
    def _get_condition_type(self, rule: Dict) -> str:
        """
        Get condition type from rule or return default
        
        Args:
            rule: The validation rule configuration
            
        Returns:
            Condition type string (AND/OR)
        """
        return rule.get("conditionType", "AND").upper()
    
    def _apply_condition_logic(self, results: list, condition_type: str) -> bool:
        """
        Apply AND/OR logic to a list of boolean results

        Args:
            results: List of boolean validation results
            condition_type: "AND" or "OR"

        Returns:
            Combined boolean result
        """
        if not results:
            raise Exception("No validation results to apply condition logic")

        if condition_type == "AND":
            return all(results)
        elif condition_type == "OR":
            return any(results)
        else:
            raise Exception(f"Unsupported condition type: {condition_type}. Supported types: AND, OR")
